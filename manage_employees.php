<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require employee management permissions
requireAnyPermission($mysqli, ['employees.create', 'employees.view', 'employees.edit', 'employees.delete']);

// Redirect to the new employee list page
header("Location: " . appUrl('employees/list.php'));
exit;
?>
