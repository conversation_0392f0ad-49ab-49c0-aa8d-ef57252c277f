<?php
session_start();

// Include permissions system
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require role management permissions
requireAnyPermission($mysqli, ['roles.view', 'roles.create', 'roles.edit']);

$msg = "";
$error = "";

// Get all roles and permissions for display
$all_roles = getAllRoles($mysqli);
$all_permissions = getAllPermissions($mysqli);

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Roles & Permissions - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <?php include 'includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header hover-lift">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Roles & Permissions</h1>
          <p class="page-subtitle">Manage system roles and their permissions</p>
        </div>
        <div class="d-flex align-items-center gap-3">
          <div class="badge bg-primary">
            <?= count($all_roles) ?> Total Roles
          </div>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Roles Overview -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-shield-check me-2"></i>
        System Roles
      </h2>
      
      <div class="row">
        <?php foreach ($all_roles as $role): ?>
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="role-card">
              <div class="role-header">
                <h5 class="role-name">
                  <?= htmlspecialchars($role['display_name']) ?>
                  <?php if ($role['is_system_role']): ?>
                    <span class="badge bg-info ms-2">System</span>
                  <?php endif; ?>
                </h5>
                <p class="role-description"><?= htmlspecialchars($role['description']) ?></p>
              </div>
              
              <div class="role-actions">
                <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'roles.view')): ?>
                  <button class="btn btn-outline-primary btn-sm" onclick="viewRolePermissions(<?= $role['id'] ?>)">
                    <i class="bi bi-eye me-1"></i>
                    View Permissions
                  </button>
                <?php endif; ?>
                
                <?php if (!$role['is_system_role'] && hasPermission($mysqli, $_SESSION['user_id'], 'roles.edit')): ?>
                  <button class="btn btn-outline-secondary btn-sm" onclick="editRole(<?= $role['id'] ?>)">
                    <i class="bi bi-pencil me-1"></i>
                    Edit
                  </button>
                <?php endif; ?>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
    </div>

    <!-- Permissions Overview -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-key me-2"></i>
        System Permissions
        <span class="badge bg-secondary ms-2"><?= array_sum(array_map('count', $all_permissions)) ?> Total</span>
      </h2>
      
      <div class="permissions-grid">
        <?php foreach ($all_permissions as $category => $permissions): ?>
          <div class="permission-category">
            <h5 class="category-title">
              <i class="bi bi-folder me-2"></i>
              <?= htmlspecialchars($category) ?>
              <span class="badge bg-light text-dark ms-2"><?= count($permissions) ?></span>
            </h5>
            
            <div class="permission-list">
              <?php foreach ($permissions as $permission): ?>
                <div class="permission-item">
                  <div class="permission-info">
                    <strong><?= htmlspecialchars($permission['display_name']) ?></strong>
                    <br>
                    <small class="text-muted"><?= htmlspecialchars($permission['description']) ?></small>
                    <br>
                    <code class="permission-code"><?= htmlspecialchars($permission['name']) ?></code>
                  </div>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-lightning me-2"></i>
        Quick Actions
      </h2>
      
      <div class="row">
        <div class="col-md-6">
          <div class="action-item">
            <h5>Create Custom Role</h5>
            <p class="text-muted">Create a new role with specific permissions for your organization's needs.</p>
            <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'roles.create')): ?>
              <button class="btn btn-primary" onclick="createRole()">
                <i class="bi bi-plus-circle me-2"></i>
                Create Role
              </button>
            <?php else: ?>
              <span class="text-muted">You don't have permission to create roles.</span>
            <?php endif; ?>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="action-item">
            <h5>Assign Roles to Users</h5>
            <p class="text-muted">Manage user role assignments and permissions.</p>
            <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'roles.assign')): ?>
              <a href="manage_users.php" class="btn btn-outline-primary">
                <i class="bi bi-person-gear me-2"></i>
                Manage User Roles
              </a>
            <?php else: ?>
              <span class="text-muted">You don't have permission to assign roles.</span>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .role-card {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 1.5rem;
      height: 100%;
      transition: all 0.2s ease;
    }
    
    .role-card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transform: translateY(-2px);
    }
    
    .role-name {
      color: #495057;
      margin-bottom: 0.5rem;
    }
    
    .role-description {
      color: #6c757d;
      font-size: 0.9rem;
      margin-bottom: 1rem;
    }
    
    .role-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
    
    .permissions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }
    
    .permission-category {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1rem;
      background: #f8f9fa;
    }
    
    .category-title {
      color: #495057;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #dee2e6;
    }
    
    .permission-item {
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      background: white;
      border-radius: 6px;
      border: 1px solid #e9ecef;
    }
    
    .permission-code {
      font-size: 0.8rem;
      background: #e9ecef;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
    }
    
    .action-item {
      padding: 1.5rem;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      height: 100%;
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function viewRolePermissions(roleId) {
      alert('View role permissions functionality will be implemented in the next update.');
    }
    
    function editRole(roleId) {
      alert('Edit role functionality will be implemented in the next update.');
    }
    
    function createRole() {
      alert('Create role functionality will be implemented in the next update.');
    }
  </script>
</body>
</html>
