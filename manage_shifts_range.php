<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed: " . $mysqli->connect_error);
}

$msg = "";
$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employee_id = intval($_POST['employee_id'] ?? 0);
    $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
    $dateRange = trim($_POST['date_range'] ?? '');

    $start_date = $end_date = '';

    if ($dateRange) {
        $dates = explode(' to ', $dateRange);
        $start_date = $dates[0] ?? '';
        $end_date = $dates[1] ?? $start_date;
    }

    if (!$employee_id || !$shift_template_id || !$start_date || !$end_date) {
        $error = "Please fill in all fields.";
    } elseif (strtotime($start_date) > strtotime($end_date)) {
        $error = "Start date cannot be after end date.";
    } else {
        $stmt = $mysqli->prepare("
            INSERT INTO employee_shifts (employee_id, shift_template_id, shift_start_date, shift_end_date)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->bind_param('iiss', $employee_id, $shift_template_id, $start_date, $end_date);
        if ($stmt->execute()) {
            $msg = "Shift assigned from $start_date to $end_date.";
        } else {
            $error = "Failed to assign shift: " . $stmt->error;
        }
        $stmt->close();
    }
}

$employees = [];
$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
}

$shift_templates = [];
$result = $mysqli->query("SELECT id, name FROM shift_templates ORDER BY name");
while ($row = $result->fetch_assoc()) {
    $shift_templates[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Assign Shift Over Date Range</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
</head>
<body>
<div class="container my-5" style="max-width:600px;">
<h1>Assign Shift to Employee Over Period</h1>

<?php if ($msg): ?>
<div class="alert alert-success"><?= htmlspecialchars($msg) ?></div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<form method="POST" action="manage_shifts_range.php" novalidate>
  <div class="mb-3">
    <label for="employee_id" class="form-label">Select Employee</label>
    <select class="form-select" id="employee_id" name="employee_id" required>
      <option value="">Choose employee</option>
      <?php foreach ($employees as $emp): ?>
      <option value="<?= $emp['id'] ?>"><?= htmlspecialchars($emp['name']) ?></option>
      <?php endforeach; ?>
    </select>
  </div>

  <div class="mb-3">
    <label for="shift_template_id" class="form-label">Select Shift Template</label>
    <select class="form-select" id="shift_template_id" name="shift_template_id" required>
      <option value="">Choose shift</option>
      <?php foreach ($shift_templates as $tpl): ?>
      <option value="<?= $tpl['id'] ?>"><?= htmlspecialchars($tpl['name']) ?></option>
      <?php endforeach; ?>
    </select>
  </div>

  <div class="mb-3">
    <label for="date_range" class="form-label">Select Date Range (Click to open calendar)</label>
    <input type="text" id="date_range" name="date_range" class="form-control" placeholder="Select date range" required />
  </div>

  <button type="submit" class="btn btn-primary">Assign Shift</button>
</form>

<p class="mt-4"><a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a></p>
</div>

<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
  flatpickr("#date_range", {
    mode: "range",
    dateFormat: "Y-m-d",
    minDate: "today"
  });
</script>
</body>
</html>
