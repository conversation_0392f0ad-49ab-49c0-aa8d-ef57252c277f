<?php
// Include configuration
require_once __DIR__ . '/config.php';

// Get current page for active navigation highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$user_name = $_SESSION['username'] ?? 'User';
$user_permissions = $_SESSION['permissions'] ?? [];
$user_roles = $_SESSION['roles'] ?? [];

// Helper function to check if user has permission (using session data for performance)
function hasSessionPermission($permission) {
    return in_array($permission, $_SESSION['permissions'] ?? []);
}

// Helper function to check if user has any of the permissions
function hasAnySessionPermission($permissions) {
    $user_permissions = $_SESSION['permissions'] ?? [];
    foreach ($permissions as $permission) {
        if (in_array($permission, $user_permissions)) {
            return true;
        }
    }
    return false;
}

// Check for super admin role specifically
$is_super_admin = in_array('super_admin', $user_roles);

// Backward compatibility check for old role system
$is_admin = in_array('super_admin', $user_roles) || in_array('admin', $user_roles) || ($_SESSION['role'] ?? '') === 'admin';

// Check if user has any management permissions
$has_employee_perms = hasAnySessionPermission(['employees.view', 'employees.create', 'employees.edit']);
$has_shift_perms = hasAnySessionPermission(['shifts.view', 'shifts.create', 'shifts.edit']);
$has_break_perms = hasAnySessionPermission(['breaks.view', 'breaks.create', 'breaks.edit']);
$has_role_perms = hasAnySessionPermission(['roles.view', 'roles.create', 'roles.edit']);
$has_user_perms = hasAnySessionPermission(['users.view', 'users.create', 'users.edit']);
$has_any_management = $has_employee_perms || $has_shift_perms || $has_break_perms || $has_role_perms || $has_user_perms;
?>

<!-- Side Navigation -->
<aside class="sidebar" id="sidebar">
  <div class="sidebar-header">
    <div class="sidebar-brand">
      <div class="sidebar-brand-icon">
        <i class="bi bi-clock-history"></i>
      </div>
      <span class="sidebar-brand-text">AttendanceApp</span>
    </div>
    <button class="sidebar-toggle" id="sidebarToggle">
      <i class="bi bi-x"></i>
    </button>
  </div>

  <nav class="sidebar-nav">
    <ul class="sidebar-menu">
      <?php if (hasSessionPermission('dashboard.view')): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('dashboard.php') ?>" class="sidebar-link <?= $current_page === 'dashboard.php' ? 'active' : '' ?>">
          <i class="bi bi-speedometer2"></i>
          <span>Dashboard</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['attendance.clock_in', 'attendance.clock_out']) && !$is_super_admin): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('index.php') ?>" class="sidebar-link <?= $current_page === 'index.php' ? 'active' : '' ?>">
          <i class="bi bi-clock"></i>
          <span>Clock In/Out</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['reports.view', 'reports.view_all'])): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('reports.php') ?>" class="sidebar-link <?= $current_page === 'reports.php' ? 'active' : '' ?>">
          <i class="bi bi-file-text"></i>
          <span>Reports</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if ($has_any_management): ?>
      <li class="sidebar-item">
        <div class="sidebar-section-title">
          <i class="bi bi-gear"></i>
          <span>Management</span>
        </div>
      </li>

      <?php if ($has_employee_perms): ?>
      <li class="sidebar-item">
        <div class="sidebar-subsection">
          <div class="sidebar-subsection-title">
            <i class="bi bi-people"></i>
            <span>Employees</span>
          </div>
          <ul class="sidebar-submenu">
            <?php if (hasAnySessionPermission(['employees.view', 'employees.edit'])): ?>
            <li>
              <a href="<?= appUrl('employees/list.php') ?>" class="sidebar-sublink <?= in_array($current_page, ['list.php']) && strpos($_SERVER['REQUEST_URI'], '/employees/') !== false ? 'active' : '' ?>">
                <i class="bi bi-list-ul"></i>
                <span>View Employees</span>
              </a>
            </li>
            <?php endif; ?>
            <?php if (hasAnySessionPermission(['employees.create'])): ?>
            <li>
              <a href="<?= appUrl('employees/create.php') ?>" class="sidebar-sublink <?= in_array($current_page, ['create.php']) && strpos($_SERVER['REQUEST_URI'], '/employees/') !== false ? 'active' : '' ?>">
                <i class="bi bi-person-plus"></i>
                <span>Add Employee</span>
              </a>
            </li>
            <?php endif; ?>
          </ul>
        </div>
      </li>
      <?php endif; ?>

      <?php if ($has_shift_perms): ?>
      <li class="sidebar-item">
        <div class="sidebar-subsection">
          <div class="sidebar-subsection-title" onclick="toggleSubmenu(this)">
            <i class="bi bi-calendar-week"></i>
            <span>Shifts</span>
            <i class="bi bi-chevron-down dropdown-arrow"></i>
          </div>
          <ul class="sidebar-submenu">
            <?php if (hasAnySessionPermission(['shifts.view', 'shifts.create', 'shifts.edit'])): ?>
            <li>
              <a href="<?= appUrl('shifts/templates.php') ?>" class="sidebar-sublink <?= in_array($current_page, ['templates.php']) && strpos($_SERVER['REQUEST_URI'], '/shifts/') !== false ? 'active' : '' ?>">
                <i class="bi bi-calendar-week"></i>
                <span>Manage Templates</span>
              </a>
            </li>
            <?php endif; ?>
            <?php if (hasAnySessionPermission(['shifts.view'])): ?>
            <li>
              <a href="<?= appUrl('shifts/assignments.php') ?>" class="sidebar-sublink <?= in_array($current_page, ['assignments.php']) && strpos($_SERVER['REQUEST_URI'], '/shifts/') !== false ? 'active' : '' ?>">
                <i class="bi bi-calendar-check"></i>
                <span>View Assignments</span>
              </a>
            </li>
            <?php endif; ?>
            <?php if (hasAnySessionPermission(['shifts.assign'])): ?>
            <li>
              <a href="<?= appUrl('shifts/assign.php') ?>" class="sidebar-sublink <?= in_array($current_page, ['assign.php']) && strpos($_SERVER['REQUEST_URI'], '/shifts/') !== false ? 'active' : '' ?>">
                <i class="bi bi-person-plus"></i>
                <span>Assign Shifts</span>
              </a>
            </li>
            <?php endif; ?>
          </ul>
        </div>
      </li>
      <?php endif; ?>

      <?php if ($has_break_perms): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('manage_breaks.php') ?>" class="sidebar-link <?= $current_page === 'manage_breaks.php' ? 'active' : '' ?>">
          <i class="bi bi-pause-circle"></i>
          <span>Breaks</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if ($has_role_perms): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('manage_roles.php') ?>" class="sidebar-link <?= $current_page === 'manage_roles.php' ? 'active' : '' ?>">
          <i class="bi bi-shield-check"></i>
          <span>Roles & Permissions</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if ($has_user_perms): ?>
      <li class="sidebar-item">
        <a href="<?= appUrl('manage_users.php') ?>" class="sidebar-link <?= $current_page === 'manage_users.php' ? 'active' : '' ?>">
          <i class="bi bi-person-gear"></i>
          <span>User Accounts</span>
        </a>
      </li>
      <?php endif; ?>
      <?php endif; ?>
    </ul>
  </nav>
</aside>

<!-- Top Navigation -->
<header class="topbar">
  <div class="topbar-container">
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
      <i class="bi bi-list"></i>
    </button>

    <!-- Page Title or Breadcrumb -->
    <div class="topbar-title">
      <h1 class="page-title">
        <?php
        $page_titles = [
          'dashboard.php' => 'Dashboard',
          'index.php' => 'Clock In/Out',
          'reports.php' => 'Reports',
          'manage_employees.php' => 'Employees',
          'list.php' => 'Employee List',
          'create.php' => 'Add Employee',
          'manage_shifts.php' => 'Shifts',
          'templates.php' => 'Shift Templates',
          'assignments.php' => 'Shift Assignments',
          'assign.php' => 'Assign Shifts',
          'manage_breaks.php' => 'Breaks',
          'manage_roles.php' => 'Roles & Permissions',
          'manage_users.php' => 'User Accounts'
        ];
        echo $page_titles[$current_page] ?? 'Attendance App';
        ?>
      </h1>
    </div>

    <!-- Date and Time Display -->
    <div class="topbar-datetime">
      <div class="datetime-container">
        <div class="current-date">
          <i class="bi bi-calendar3"></i>
          <span><?= date('M j, Y') ?></span>
        </div>
        <div class="current-time">
          <i class="bi bi-clock"></i>
          <span id="topbarCurrentTime"><?= date('g:i A') ?></span>
        </div>
      </div>
    </div>

    <!-- User Menu -->
    <div class="topbar-user">
      <div class="user-dropdown">
        <button class="user-dropdown-toggle" id="userDropdownToggle">
          <div class="user-avatar">
            <i class="bi bi-person-circle"></i>
          </div>
          <span class="user-name"><?= htmlspecialchars($user_name) ?></span>
          <i class="bi bi-chevron-down"></i>
        </button>

        <div class="user-dropdown-menu" id="userDropdownMenu">
          <div class="user-dropdown-header">
            <div class="user-info">
              <div class="user-avatar-large">
                <i class="bi bi-person-circle"></i>
              </div>
              <div class="user-details">
                <div class="user-name-large"><?= htmlspecialchars($user_name) ?></div>
                <div class="user-role"><?= implode(', ', array_map('ucfirst', $user_roles)) ?></div>
              </div>
            </div>
          </div>

          <div class="user-dropdown-body">
            <button class="user-dropdown-item" id="themeToggle">
              <i class="bi bi-moon" id="themeIcon"></i>
              <span>Dark Mode</span>
              <div class="toggle-switch">
                <div class="toggle-slider"></div>
              </div>
            </button>

            <a href="logout.php" class="user-dropdown-item logout-item" onclick="return confirm('Are you sure you want to logout?')">
              <i class="bi bi-box-arrow-right"></i>
              <span>Logout</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
  <div class="loading-spinner"></div>
</div>

<!-- Notification Container -->
<div id="notificationContainer"></div>

<script>
// Global Navigation JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Update time every second in topbar
  function updateTopbarTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    const timeElement = document.getElementById('topbarCurrentTime');
    if (timeElement) {
      timeElement.textContent = timeString;
    }
  }

  // Update time immediately and then every second
  updateTopbarTime();
  setInterval(updateTopbarTime, 1000);

  // Theme Toggle
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = document.getElementById('themeIcon');
  const body = document.body;

  // Load saved theme
  const savedTheme = localStorage.getItem('theme') || 'light';
  body.setAttribute('data-theme', savedTheme);
  updateThemeIcon(savedTheme);
  updateToggleSwitch(savedTheme);

  themeToggle.addEventListener('click', function() {
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
    updateToggleSwitch(newTheme);

    // Add bounce animation
    themeToggle.classList.add('animate-bounce');
    setTimeout(() => themeToggle.classList.remove('animate-bounce'), 1000);
  });

  function updateThemeIcon(theme) {
    themeIcon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
  }

  function updateToggleSwitch(theme) {
    const toggleSwitch = document.querySelector('.toggle-switch');
    if (toggleSwitch) {
      toggleSwitch.classList.toggle('active', theme === 'dark');
    }
  }

  // Sidebar Toggle
  const mobileMenuToggle = document.getElementById('mobileMenuToggle');
  const sidebarToggle = document.getElementById('sidebarToggle');
  const sidebar = document.getElementById('sidebar');
  const sidebarOverlay = document.getElementById('sidebarOverlay');

  function toggleSidebar() {
    sidebar.classList.toggle('active');
    sidebarOverlay.classList.toggle('active');
    body.classList.toggle('sidebar-open');
  }

  function closeSidebar() {
    sidebar.classList.remove('active');
    sidebarOverlay.classList.remove('active');
    body.classList.remove('sidebar-open');
  }

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener('click', toggleSidebar);
  }

  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', closeSidebar);
  }

  if (sidebarOverlay) {
    sidebarOverlay.addEventListener('click', closeSidebar);
  }

  // User Dropdown
  const userDropdownToggle = document.getElementById('userDropdownToggle');
  const userDropdownMenu = document.getElementById('userDropdownMenu');

  if (userDropdownToggle && userDropdownMenu) {
    userDropdownToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      userDropdownMenu.classList.toggle('show');
    });

    // Close user dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!userDropdownToggle.contains(e.target) && !userDropdownMenu.contains(e.target)) {
        userDropdownMenu.classList.remove('show');
      }
    });
  }

  // Close sidebar on window resize if it's open on mobile
  window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
      closeSidebar();
    }
  });

  // Page Transition Animation
  body.classList.add('page-transition');
  setTimeout(() => {
    body.classList.add('loaded');
  }, 100);

  // Add loading state to forms
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function() {
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        addLoadingState(submitBtn);
      }
    });
  });
});

// Global Functions
function showLoading() {
  document.getElementById('loadingOverlay').classList.add('active');
}

function hideLoading() {
  document.getElementById('loadingOverlay').classList.remove('active');
}

function toggleSubmenu(element) {
  const submenu = element.nextElementSibling;
  const arrow = element.querySelector('.dropdown-arrow');

  if (submenu) {
    submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
    arrow.style.transform = submenu.style.display === 'none' ? 'rotate(0deg)' : 'rotate(180deg)';
  }
}

function addLoadingState(button) {
  button.classList.add('btn-loading');
  const text = button.innerHTML;
  button.setAttribute('data-original-text', text);
  button.innerHTML = '<span class="btn-text">' + text + '</span>';
}

function removeLoadingState(button) {
  button.classList.remove('btn-loading');
  const originalText = button.getAttribute('data-original-text');
  if (originalText) {
    button.innerHTML = originalText;
  }
}

function showNotification(message, type = 'info', duration = 5000) {
  const container = document.getElementById('notificationContainer');
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div style="display: flex; align-items: center; gap: 0.5rem;">
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
      <span>${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; margin-left: auto; cursor: pointer;">
        <i class="bi bi-x"></i>
      </button>
    </div>
  `;

  container.appendChild(notification);

  // Show notification
  setTimeout(() => notification.classList.add('show'), 100);

  // Auto remove
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => notification.remove(), 300);
  }, duration);
}

// Enhanced form handling with loading states
function handleFormSubmit(form, callback) {
  const submitBtn = form.querySelector('button[type="submit"]');

  form.addEventListener('submit', function(e) {
    e.preventDefault();

    if (submitBtn) addLoadingState(submitBtn);
    showLoading();

    // Simulate processing time or call actual callback
    setTimeout(() => {
      hideLoading();
      if (submitBtn) removeLoadingState(submitBtn);

      if (callback) callback();
    }, 1000);
  });
}
</script>
