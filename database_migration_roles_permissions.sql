-- Database Migration: Roles and Permissions System
-- Run this script to upgrade your database to support the new roles and permissions system

-- 1. Create roles table
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `is_system_role` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create permissions table
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text,
  `category` varchar(50) NOT NULL,
  `resource` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `category` (`category`),
  KEY `resource_action` (`resource`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create role_permissions table (many-to-many)
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permission` (`role_id`, `permission_id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create user_roles table (many-to-many)
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `assigned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role` (`user_id`, `role_id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  KEY `assigned_by` (`assigned_by`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_roles_ibfk_3` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Add additional fields to employees table
ALTER TABLE `employees`
ADD COLUMN IF NOT EXISTS `email` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `phone` varchar(20) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `department` varchar(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `position` varchar(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `hire_date` date DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `status` enum('active','inactive','terminated') DEFAULT 'active',
ADD COLUMN IF NOT EXISTS `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 6. Add unique constraint on email if it doesn't exist
ALTER TABLE `employees` ADD UNIQUE KEY IF NOT EXISTS `email` (`email`);

-- 7. Insert default roles
INSERT IGNORE INTO `roles` (`name`, `display_name`, `description`, `is_system_role`) VALUES
('super_admin', 'Super Administrator', 'Full system access with all permissions', 1),
('admin', 'Administrator', 'Administrative access to most system features', 1),
('shift_manager', 'Shift Manager', 'Can manage shifts and employee schedules', 0),
('hr_manager', 'HR Manager', 'Can manage employees and user accounts', 0),
('employee', 'Employee', 'Basic employee access for attendance tracking', 1);

-- 8. Insert default permissions
INSERT IGNORE INTO `permissions` (`name`, `display_name`, `description`, `category`, `resource`, `action`) VALUES
-- Dashboard permissions
('dashboard.view', 'View Dashboard', 'Access to main dashboard', 'Dashboard', 'dashboard', 'view'),
('dashboard.admin_stats', 'View Admin Statistics', 'View administrative statistics on dashboard', 'Dashboard', 'dashboard', 'admin_stats'),

-- Employee management permissions
('employees.view', 'View Employees', 'View employee list and details', 'Employee Management', 'employees', 'view'),
('employees.create', 'Create Employees', 'Add new employees to the system', 'Employee Management', 'employees', 'create'),
('employees.edit', 'Edit Employees', 'Modify employee information', 'Employee Management', 'employees', 'edit'),
('employees.delete', 'Delete Employees', 'Remove employees from the system', 'Employee Management', 'employees', 'delete'),

-- User account management permissions
('users.view', 'View User Accounts', 'View user account list and details', 'User Management', 'users', 'view'),
('users.create', 'Create User Accounts', 'Create new user accounts', 'User Management', 'users', 'create'),
('users.edit', 'Edit User Accounts', 'Modify user account information', 'User Management', 'users', 'edit'),
('users.delete', 'Delete User Accounts', 'Remove user accounts', 'User Management', 'users', 'delete'),
('users.reset_password', 'Reset User Passwords', 'Reset passwords for user accounts', 'User Management', 'users', 'reset_password'),

-- Role and permission management
('roles.view', 'View Roles', 'View system roles and permissions', 'Role Management', 'roles', 'view'),
('roles.create', 'Create Roles', 'Create new system roles', 'Role Management', 'roles', 'create'),
('roles.edit', 'Edit Roles', 'Modify existing roles and their permissions', 'Role Management', 'roles', 'edit'),
('roles.delete', 'Delete Roles', 'Remove system roles', 'Role Management', 'roles', 'delete'),
('roles.assign', 'Assign Roles', 'Assign roles to users', 'Role Management', 'roles', 'assign'),

-- Shift management permissions
('shifts.view', 'View Shifts', 'View shift schedules and templates', 'Shift Management', 'shifts', 'view'),
('shifts.create', 'Create Shifts', 'Create new shift templates and assignments', 'Shift Management', 'shifts', 'create'),
('shifts.edit', 'Edit Shifts', 'Modify shift templates and assignments', 'Shift Management', 'shifts', 'edit'),
('shifts.delete', 'Delete Shifts', 'Remove shift templates and assignments', 'Shift Management', 'shifts', 'delete'),
('shifts.assign', 'Assign Shifts', 'Assign shifts to employees', 'Shift Management', 'shifts', 'assign'),

-- Break management permissions
('breaks.view', 'View Breaks', 'View break schedules and templates', 'Break Management', 'breaks', 'view'),
('breaks.create', 'Create Breaks', 'Create new break templates', 'Break Management', 'breaks', 'create'),
('breaks.edit', 'Edit Breaks', 'Modify break templates', 'Break Management', 'breaks', 'edit'),
('breaks.delete', 'Delete Breaks', 'Remove break templates', 'Break Management', 'breaks', 'delete'),

-- Attendance permissions
('attendance.view', 'View Attendance', 'View attendance records', 'Attendance', 'attendance', 'view'),
('attendance.clock_in', 'Clock In', 'Record clock in attendance', 'Attendance', 'attendance', 'clock_in'),
('attendance.clock_out', 'Clock Out', 'Record clock out attendance', 'Attendance', 'attendance', 'clock_out'),
('attendance.view_all', 'View All Attendance', 'View attendance records for all employees', 'Attendance', 'attendance', 'view_all'),
('attendance.edit', 'Edit Attendance', 'Modify attendance records', 'Attendance', 'attendance', 'edit'),
('attendance.delete', 'Delete Attendance', 'Remove attendance records', 'Attendance', 'attendance', 'delete'),

-- Reports permissions
('reports.view', 'View Reports', 'Access to attendance reports', 'Reports', 'reports', 'view'),
('reports.view_all', 'View All Reports', 'View reports for all employees', 'Reports', 'reports', 'view_all'),
('reports.export', 'Export Reports', 'Export reports to various formats', 'Reports', 'reports', 'export'),

-- System permissions
('system.settings', 'System Settings', 'Access to system configuration', 'System', 'system', 'settings'),
('system.backup', 'System Backup', 'Create and manage system backups', 'System', 'system', 'backup'),
('system.logs', 'View System Logs', 'Access to system logs and audit trails', 'System', 'system', 'logs');

-- 9. Assign permissions to default roles

-- Super Admin gets all permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'super_admin';

-- Admin gets most permissions (excluding some system-level ones)
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'admin'
AND p.name NOT IN ('system.backup', 'system.logs', 'roles.delete', 'users.delete');

-- Shift Manager permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'shift_manager'
AND p.name IN (
    'dashboard.view',
    'employees.view',
    'shifts.view', 'shifts.create', 'shifts.edit', 'shifts.assign',
    'breaks.view', 'breaks.create', 'breaks.edit',
    'attendance.clock_in', 'attendance.clock_out', 'attendance.view', 'attendance.view_all',
    'reports.view', 'reports.view_all'
);

-- HR Manager permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'hr_manager'
AND p.name IN (
    'dashboard.view',
    'employees.view', 'employees.create', 'employees.edit',
    'users.view', 'users.create', 'users.edit', 'users.reset_password',
    'roles.view', 'roles.assign',
    'attendance.view', 'attendance.view_all',
    'reports.view', 'reports.view_all', 'reports.export'
);

-- Employee permissions (basic)
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'employee'
AND p.name IN (
    'dashboard.view',
    'attendance.clock_in', 'attendance.clock_out', 'attendance.view',
    'reports.view'
);

-- 10. Migrate existing admin user to new role system
-- First, assign super_admin role to existing admin user
INSERT IGNORE INTO `user_roles` (`user_id`, `role_id`)
SELECT u.id, r.id
FROM `users` u
CROSS JOIN `roles` r
WHERE u.username = 'admin' AND r.name = 'super_admin';

-- 11. Add a backup column to preserve old role data temporarily
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `old_role` varchar(20) DEFAULT NULL;

-- Update the backup column with current role data
UPDATE `users` SET `old_role` = `role` WHERE `old_role` IS NULL;

-- 12. Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_user_roles_user` ON `user_roles` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_user_roles_role` ON `user_roles` (`role_id`);
CREATE INDEX IF NOT EXISTS `idx_role_permissions_role` ON `role_permissions` (`role_id`);
CREATE INDEX IF NOT EXISTS `idx_role_permissions_permission` ON `role_permissions` (`permission_id`);
CREATE INDEX IF NOT EXISTS `idx_permissions_category` ON `permissions` (`category`);
CREATE INDEX IF NOT EXISTS `idx_permissions_resource` ON `permissions` (`resource`);

-- 13. Add some sample data for testing (optional)
-- You can uncomment these if you want sample employees with different roles

/*
-- Sample employees
INSERT IGNORE INTO `employees` (`name`, `email`, `department`, `position`, `hire_date`, `status`) VALUES
('John Smith', '<EMAIL>', 'Operations', 'Shift Manager', '2024-01-15', 'active'),
('Sarah Johnson', '<EMAIL>', 'Human Resources', 'HR Manager', '2024-02-01', 'active'),
('Mike Wilson', '<EMAIL>', 'Operations', 'Employee', '2024-03-01', 'active'),
('Lisa Brown', '<EMAIL>', 'Operations', 'Employee', '2024-03-15', 'active');

-- Sample user accounts for the employees
INSERT IGNORE INTO `users` (`employee_id`, `username`, `password_hash`, `old_role`) VALUES
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'john.smith', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'sarah.johnson', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'mike.wilson', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'lisa.brown', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee');

-- Assign roles to sample users
INSERT IGNORE INTO `user_roles` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `users` u CROSS JOIN `roles` r
WHERE u.username = 'john.smith' AND r.name = 'shift_manager';

INSERT IGNORE INTO `user_roles` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `users` u CROSS JOIN `roles` r
WHERE u.username = 'sarah.johnson' AND r.name = 'hr_manager';

INSERT IGNORE INTO `user_roles` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `users` u CROSS JOIN `roles` r
WHERE u.username IN ('mike.wilson', 'lisa.brown') AND r.name = 'employee';
*/

-- Migration completed successfully!
-- Note: The old 'role' column in users table is preserved as 'old_role' for backup purposes
-- You can drop it after confirming the migration works correctly:
-- ALTER TABLE `users` DROP COLUMN `role`;
-- ALTER TABLE `users` DROP COLUMN `old_role`;
