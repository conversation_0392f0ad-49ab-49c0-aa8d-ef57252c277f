<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/permissions.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

// Refresh user session with latest permissions
$success = refreshUserSession($mysqli);

if ($success) {
    $msg = "Session refreshed successfully! Your permissions have been updated.";
} else {
    $msg = "Failed to refresh session. Please try logging out and logging back in.";
}

// Redirect back to dashboard with message
header("Location: " . appUrl('dashboard.php') . "?refresh_msg=" . urlencode($msg));
exit;
?>
