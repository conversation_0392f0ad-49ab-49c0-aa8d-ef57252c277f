<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift template management permissions
requireAnyPermission($mysqli, ['shifts.view', 'shifts.create', 'shifts.edit']);

$msg = "";
$error = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create')) {
        $error = "You don't have permission to create shift templates.";
      } else {
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.00);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.50);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.50);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.00);
        $overtime_multiplier = floatval($_POST['overtime_rate_multiplier'] ?? 1.50);
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            INSERT INTO shift_templates (
              name, start_time, end_time, description,
              early_checkin_allowed_hours, late_checkin_grace_hours, late_checkin_policy,
              early_checkout_penalty_hours, late_checkout_overtime_hours, overtime_rate_multiplier,
              weekend_policy_enabled, holiday_policy_enabled
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ");
          $stmt->bind_param('ssssddsdddii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_multiplier,
            $weekend_policy_enabled, $holiday_policy_enabled
          );
          if ($stmt->execute()) {
            $msg = "Shift template \"$name\" created successfully with attendance policies.";
          } else {
            $error = "Failed to create shift template. It may already exist.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'edit_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit')) {
        $error = "You don't have permission to edit shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.00);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.50);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.50);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.00);
        $overtime_multiplier = floatval($_POST['overtime_rate_multiplier'] ?? 1.50);
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($template_id && $name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            UPDATE shift_templates SET
              name = ?, start_time = ?, end_time = ?, description = ?,
              early_checkin_allowed_hours = ?, late_checkin_grace_hours = ?, late_checkin_policy = ?,
              early_checkout_penalty_hours = ?, late_checkout_overtime_hours = ?, overtime_rate_multiplier = ?,
              weekend_policy_enabled = ?, holiday_policy_enabled = ?
            WHERE id = ?
          ");
          $stmt->bind_param('ssssddsdddiii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_multiplier,
            $weekend_policy_enabled, $holiday_policy_enabled, $template_id
          );
          if ($stmt->execute()) {
            $msg = "Shift template updated successfully with attendance policies.";
          } else {
            $error = "Failed to update shift template.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'delete_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete')) {
        $error = "You don't have permission to delete shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        if ($template_id) {
          // Check if template is being used
          $check_stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM employee_shifts WHERE shift_template_id = ?");
          $check_stmt->bind_param('i', $template_id);
          $check_stmt->execute();
          $check_stmt->bind_result($usage_count);
          $check_stmt->fetch();
          $check_stmt->close();

          if ($usage_count > 0) {
            $error = "Cannot delete shift template. It is currently assigned to $usage_count employee(s).";
          } else {
            $stmt = $mysqli->prepare("DELETE FROM shift_templates WHERE id = ?");
            $stmt->bind_param('i', $template_id);
            if ($stmt->execute()) {
              $msg = "Shift template deleted successfully.";
            } else {
              $error = "Failed to delete shift template.";
            }
            $stmt->close();
          }
        }
      }
    }
  }
}

// Fetch shift templates with attendance policies
$templates_result = $mysqli->query("
  SELECT *,
    CASE
      WHEN weekend_policy_enabled = 1 THEN 'Weekend policies enabled'
      ELSE ''
    END as weekend_status,
    CASE
      WHEN holiday_policy_enabled = 1 THEN 'Holiday policies enabled'
      ELSE ''
    END as holiday_status
  FROM shift_templates
  ORDER BY name
");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Check user permissions for UI
$can_create = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create');
$can_edit = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit');
$can_delete = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete');
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Templates - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">


    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Create New Template -->
    <?php if ($can_create): ?>
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-plus-circle me-2"></i>
        Create New Shift Template
      </h2>
      <form method="POST" class="row g-3">
        <input type="hidden" name="action" value="add_template" />

        <div class="col-md-6">
          <label for="name" class="form-label">Template Name</label>
          <input type="text" class="form-control" id="name" name="name" required
                 placeholder="e.g., Morning Shift, Night Shift" />
        </div>

        <div class="col-md-3">
          <label for="start_time" class="form-label">Start Time</label>
          <input type="time" class="form-control" id="start_time" name="start_time" required />
        </div>

        <div class="col-md-3">
          <label for="end_time" class="form-label">End Time</label>
          <input type="time" class="form-control" id="end_time" name="end_time" required />
        </div>

        <div class="col-12">
          <label for="description" class="form-label">Description (Optional)</label>
          <textarea class="form-control" id="description" name="description" rows="2"
                    placeholder="Brief description of this shift template"></textarea>
        </div>

        <!-- Attendance Policies Section -->
        <div class="col-12">
          <hr class="my-4">
          <h5 class="mb-3">
            <i class="bi bi-clock-history me-2"></i>
            Attendance Policies
          </h5>
        </div>

        <div class="col-md-6">
          <label for="early_checkin_allowed_hours" class="form-label">Early Check-in Allowed (hours)</label>
          <input type="number" class="form-control" id="early_checkin_allowed_hours"
                 name="early_checkin_allowed_hours" step="0.25" min="0" max="8" value="1.00" />
          <small class="form-text text-muted">Hours before shift start allowed for overtime calculation</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_grace_hours" class="form-label">Late Check-in Grace Period (hours)</label>
          <input type="number" class="form-control" id="late_checkin_grace_hours"
                 name="late_checkin_grace_hours" step="0.25" min="0" max="4" value="0.50" />
          <small class="form-text text-muted">Grace period before marking as absent</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_policy" class="form-label">Late Check-in Policy</label>
          <select class="form-select" id="late_checkin_policy" name="late_checkin_policy">
            <option value="mark_late">Mark as Late</option>
            <option value="mark_absent">Mark as Absent</option>
          </select>
          <small class="form-text text-muted">Action for arrivals beyond grace period</small>
        </div>

        <div class="col-md-6">
          <label for="early_checkout_penalty_hours" class="form-label">Early Check-out Penalty (hours)</label>
          <input type="number" class="form-control" id="early_checkout_penalty_hours"
                 name="early_checkout_penalty_hours" step="0.25" min="0" max="4" value="0.50" />
          <small class="form-text text-muted">Hours before shift end that trigger penalty</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkout_overtime_hours" class="form-label">Late Check-out Overtime Limit (hours)</label>
          <input type="number" class="form-control" id="late_checkout_overtime_hours"
                 name="late_checkout_overtime_hours" step="0.25" min="0" max="8" value="2.00" />
          <small class="form-text text-muted">Hours after shift end allowed for overtime</small>
        </div>

        <div class="col-md-6">
          <label for="overtime_rate_multiplier" class="form-label">Overtime Rate Multiplier</label>
          <input type="number" class="form-control" id="overtime_rate_multiplier"
                 name="overtime_rate_multiplier" step="0.25" min="1" max="3" value="1.50" />
          <small class="form-text text-muted">Overtime pay multiplier (e.g., 1.5 = time and a half)</small>
        </div>

        <div class="col-12">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="weekend_policy_enabled" name="weekend_policy_enabled">
            <label class="form-check-label" for="weekend_policy_enabled">
              Enable Different Weekend Policies
            </label>
            <small class="form-text text-muted d-block">Use different attendance rules for weekends</small>
          </div>
        </div>

        <div class="col-12">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="holiday_policy_enabled" name="holiday_policy_enabled">
            <label class="form-check-label" for="holiday_policy_enabled">
              Enable Different Holiday Policies
            </label>
            <small class="form-text text-muted d-block">Use different attendance rules for holidays</small>
          </div>
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i>
            Create Template
          </button>
        </div>
      </form>
    </div>
    <?php endif; ?>

    <!-- Existing Templates -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-list-ul me-2"></i>
        Existing Shift Templates
      </h2>

      <?php if (empty($shift_templates)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Shift Templates Found</h4>
          <p class="text-muted">Create your first shift template to get started.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Template Name</th>
                <th>Start Time</th>
                <th>End Time</th>
                <th>Duration</th>
                <th>Attendance Policies</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($shift_templates as $template): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($template['name']) ?></strong>
                </td>
                <td><?= date('g:i A', strtotime($template['start_time'])) ?></td>
                <td><?= date('g:i A', strtotime($template['end_time'])) ?></td>
                <td>
                  <?php
                  $start = new DateTime($template['start_time']);
                  $end = new DateTime($template['end_time']);
                  if ($end < $start) $end->add(new DateInterval('P1D')); // Next day
                  $duration = $start->diff($end);
                  echo $duration->format('%h hours %i minutes');
                  ?>
                </td>
                <td>
                  <small class="text-muted">
                    <div><strong>Early Check-in:</strong> <?= $template['early_checkin_allowed_hours'] ?>h</div>
                    <div><strong>Grace Period:</strong> <?= $template['late_checkin_grace_hours'] ?>h</div>
                    <div><strong>Overtime Rate:</strong> <?= $template['overtime_rate_multiplier'] ?>x</div>
                    <?php if ($template['weekend_policy_enabled']): ?>
                      <span class="badge bg-info">Weekend Policies</span>
                    <?php endif; ?>
                    <?php if ($template['holiday_policy_enabled']): ?>
                      <span class="badge bg-warning">Holiday Policies</span>
                    <?php endif; ?>
                  </small>
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <?php if ($can_edit): ?>
                    <button type="button" class="btn btn-outline-primary btn-sm"
                            onclick="editTemplate(<?= $template['id'] ?>,
                              '<?= htmlspecialchars($template['name']) ?>',
                              '<?= $template['start_time'] ?>',
                              '<?= $template['end_time'] ?>',
                              '<?= htmlspecialchars($template['description']) ?>',
                              <?= $template['early_checkin_allowed_hours'] ?>,
                              <?= $template['late_checkin_grace_hours'] ?>,
                              '<?= $template['late_checkin_policy'] ?>',
                              <?= $template['early_checkout_penalty_hours'] ?>,
                              <?= $template['late_checkout_overtime_hours'] ?>,
                              <?= $template['overtime_rate_multiplier'] ?>,
                              <?= $template['weekend_policy_enabled'] ? 'true' : 'false' ?>,
                              <?= $template['holiday_policy_enabled'] ? 'true' : 'false' ?>)">
                      <i class="bi bi-pencil"></i>
                    </button>
                    <?php endif; ?>

                    <?php if ($can_delete): ?>
                    <form method="POST" style="display: inline;"
                          onsubmit="return confirm('Are you sure you want to delete this template?')">
                      <input type="hidden" name="action" value="delete_template" />
                      <input type="hidden" name="template_id" value="<?= $template['id'] ?>" />
                      <button type="submit" class="btn btn-outline-danger btn-sm">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                    <?php endif; ?>
                  </div>
                </td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <!-- Edit Template Modal -->
  <div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Edit Shift Template</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form method="POST" id="editTemplateForm">
          <div class="modal-body">
            <input type="hidden" name="action" value="edit_template" />
            <input type="hidden" name="template_id" id="edit_template_id" />

            <div class="row g-3">
              <div class="col-12">
                <label for="edit_name" class="form-label">Template Name</label>
                <input type="text" class="form-control" id="edit_name" name="name" required />
              </div>

              <div class="col-6">
                <label for="edit_start_time" class="form-label">Start Time</label>
                <input type="time" class="form-control" id="edit_start_time" name="start_time" required />
              </div>
              <div class="col-6">
                <label for="edit_end_time" class="form-label">End Time</label>
                <input type="time" class="form-control" id="edit_end_time" name="end_time" required />
              </div>

              <div class="col-12">
                <label for="edit_description" class="form-label">Description</label>
                <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
              </div>

              <!-- Attendance Policies Section -->
              <div class="col-12">
                <hr class="my-3">
                <h6 class="mb-3">
                  <i class="bi bi-clock-history me-2"></i>
                  Attendance Policies
                </h6>
              </div>

              <div class="col-md-6">
                <label for="edit_early_checkin_allowed_hours" class="form-label">Early Check-in Allowed (hours)</label>
                <input type="number" class="form-control" id="edit_early_checkin_allowed_hours"
                       name="early_checkin_allowed_hours" step="0.25" min="0" max="8" />
              </div>

              <div class="col-md-6">
                <label for="edit_late_checkin_grace_hours" class="form-label">Late Check-in Grace Period (hours)</label>
                <input type="number" class="form-control" id="edit_late_checkin_grace_hours"
                       name="late_checkin_grace_hours" step="0.25" min="0" max="4" />
              </div>

              <div class="col-md-6">
                <label for="edit_late_checkin_policy" class="form-label">Late Check-in Policy</label>
                <select class="form-select" id="edit_late_checkin_policy" name="late_checkin_policy">
                  <option value="mark_late">Mark as Late</option>
                  <option value="mark_absent">Mark as Absent</option>
                </select>
              </div>

              <div class="col-md-6">
                <label for="edit_early_checkout_penalty_hours" class="form-label">Early Check-out Penalty (hours)</label>
                <input type="number" class="form-control" id="edit_early_checkout_penalty_hours"
                       name="early_checkout_penalty_hours" step="0.25" min="0" max="4" />
              </div>

              <div class="col-md-6">
                <label for="edit_late_checkout_overtime_hours" class="form-label">Late Check-out Overtime Limit (hours)</label>
                <input type="number" class="form-control" id="edit_late_checkout_overtime_hours"
                       name="late_checkout_overtime_hours" step="0.25" min="0" max="8" />
              </div>

              <div class="col-md-6">
                <label for="edit_overtime_rate_multiplier" class="form-label">Overtime Rate Multiplier</label>
                <input type="number" class="form-control" id="edit_overtime_rate_multiplier"
                       name="overtime_rate_multiplier" step="0.25" min="1" max="3" />
              </div>

              <div class="col-12">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="edit_weekend_policy_enabled" name="weekend_policy_enabled">
                  <label class="form-check-label" for="edit_weekend_policy_enabled">
                    Enable Different Weekend Policies
                  </label>
                </div>
              </div>

              <div class="col-12">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="edit_holiday_policy_enabled" name="holiday_policy_enabled">
                  <label class="form-check-label" for="edit_holiday_policy_enabled">
                    Enable Different Holiday Policies
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary">Update Template</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function editTemplate(id, name, startTime, endTime, description,
                         earlyCheckinAllowed, lateCheckinGrace, lateCheckinPolicy,
                         earlyCheckoutPenalty, lateCheckoutOvertime, overtimeMultiplier,
                         weekendPolicyEnabled, holidayPolicyEnabled) {
      // Basic fields
      document.getElementById('edit_template_id').value = id;
      document.getElementById('edit_name').value = name;
      document.getElementById('edit_start_time').value = startTime;
      document.getElementById('edit_end_time').value = endTime;
      document.getElementById('edit_description').value = description;

      // Attendance policy fields
      document.getElementById('edit_early_checkin_allowed_hours').value = earlyCheckinAllowed;
      document.getElementById('edit_late_checkin_grace_hours').value = lateCheckinGrace;
      document.getElementById('edit_late_checkin_policy').value = lateCheckinPolicy;
      document.getElementById('edit_early_checkout_penalty_hours').value = earlyCheckoutPenalty;
      document.getElementById('edit_late_checkout_overtime_hours').value = lateCheckoutOvertime;
      document.getElementById('edit_overtime_rate_multiplier').value = overtimeMultiplier;
      document.getElementById('edit_weekend_policy_enabled').checked = weekendPolicyEnabled === 'true';
      document.getElementById('edit_holiday_policy_enabled').checked = holidayPolicyEnabled === 'true';

      new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
    }
  </script>
</body>
</html>
