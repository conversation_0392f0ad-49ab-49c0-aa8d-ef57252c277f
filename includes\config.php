<?php
/**
 * Configuration file for Attendance App
 */

// Application configuration
// Change this to match your application's directory name
// Examples:
//   - If your app is at http://localhost/attendance_app2/ set to 'attendance_app2'
//   - If your app is at http://localhost/myapp/ set to 'myapp'
//   - If your app is at http://localhost/ (document root) set to ''
define('APP_DIRECTORY', 'attendance_app2');

// Get the application root path (relative to document root)
function getAppPath() {
    // If APP_DIRECTORY is empty, we're in the document root
    if (empty(APP_DIRECTORY)) {
        return '';
    }

    return '/' . APP_DIRECTORY;
}

// Generate a URL relative to the application root
function appUrl($path = '') {
    $appPath = getAppPath();
    $path = ltrim($path, '/');

    if (empty($path)) {
        return $appPath ?: '/';
    }

    if (empty($appPath)) {
        return '/' . $path;
    }

    return $appPath . '/' . $path;
}

// Get the base URL dynamically
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $appPath = getAppPath();

    return $protocol . '://' . $host . $appPath;
}

// Define constants
define('APP_PATH', getAppPath());
define('BASE_URL', getBaseUrl());
?>
