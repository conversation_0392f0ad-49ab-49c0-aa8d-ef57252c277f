-- =====================================================
-- Advanced Attendance Management System Migration
-- Version 2.0.0
-- =====================================================

-- 1. Update shift_templates table with attendance policy fields
ALTER TABLE `shift_templates` ADD COLUMN IF NOT EXISTS (
  `early_checkin_allowed_hours` DECIMAL(3,2) NOT NULL DEFAULT 1.00 COMMENT 'Hours before shift start allowed for overtime',
  `late_checkin_grace_hours` DECIMAL(3,2) NOT NULL DEFAULT 0.50 COMMENT 'Grace period for late arrivals in hours',
  `late_checkin_policy` ENUM('mark_late', 'mark_absent') NOT NULL DEFAULT 'mark_late' COMMENT 'Action for arrivals beyond grace period',
  `early_checkout_penalty_hours` DECIMAL(3,2) NOT NULL DEFAULT 0.50 COMMENT 'Hours before shift end that trigger penalty',
  `late_checkout_overtime_hours` DECIMAL(3,2) NOT NULL DEFAULT 2.00 COMMENT 'Hours after shift end allowed for overtime',
  `overtime_rate_multiplier` DECIMAL(3,2) NOT NULL DEFAULT 1.50 COMMENT 'Overtime pay multiplier',
  `weekend_policy_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Use different rules for weekends',
  `holiday_policy_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Use different rules for holidays',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Create attendance_policies table for weekend/holiday rules
CREATE TABLE IF NOT EXISTS `attendance_policies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_template_id` int(11) NOT NULL,
  `policy_type` ENUM('weekend', 'holiday') NOT NULL,
  `early_checkin_allowed_hours` DECIMAL(3,2) NOT NULL DEFAULT 1.00,
  `late_checkin_grace_hours` DECIMAL(3,2) NOT NULL DEFAULT 0.50,
  `late_checkin_policy` ENUM('mark_late', 'mark_absent') NOT NULL DEFAULT 'mark_late',
  `early_checkout_penalty_hours` DECIMAL(3,2) NOT NULL DEFAULT 0.50,
  `late_checkout_overtime_hours` DECIMAL(3,2) NOT NULL DEFAULT 2.00,
  `overtime_rate_multiplier` DECIMAL(3,2) NOT NULL DEFAULT 2.00,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_policy` (`shift_template_id`, `policy_type`),
  FOREIGN KEY (`shift_template_id`) REFERENCES `shift_templates`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create attendance_adjustments table for manager overrides
CREATE TABLE IF NOT EXISTS `attendance_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attendance_id` int(11) NOT NULL,
  `adjusted_by_user_id` int(11) NOT NULL,
  `adjustment_type` ENUM('time_correction', 'status_change', 'overtime_adjustment', 'break_adjustment') NOT NULL,
  `original_timestamp` DATETIME NULL,
  `adjusted_timestamp` DATETIME NULL,
  `original_status` VARCHAR(50) NULL,
  `adjusted_status` VARCHAR(50) NULL,
  `original_overtime_hours` DECIMAL(4,2) NULL DEFAULT 0.00,
  `adjusted_overtime_hours` DECIMAL(4,2) NULL DEFAULT 0.00,
  `original_break_hours` DECIMAL(4,2) NULL DEFAULT 0.00,
  `adjusted_break_hours` DECIMAL(4,2) NULL DEFAULT 0.00,
  `reason` TEXT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`attendance_id`) REFERENCES `attendance`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`adjusted_by_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Update attendance table with calculated fields
ALTER TABLE `attendance` ADD COLUMN IF NOT EXISTS (
  `shift_template_id` int(11) NULL COMMENT 'Reference to assigned shift template',
  `scheduled_start_time` TIME NULL COMMENT 'Scheduled shift start time',
  `scheduled_end_time` TIME NULL COMMENT 'Scheduled shift end time',
  `attendance_status` ENUM('on_time', 'late', 'very_late', 'early', 'absent') NOT NULL DEFAULT 'on_time',
  `overtime_hours` DECIMAL(4,2) NOT NULL DEFAULT 0.00 COMMENT 'Overtime hours earned',
  `late_hours` DECIMAL(4,2) NOT NULL DEFAULT 0.00 COMMENT 'Hours late',
  `break_hours` DECIMAL(4,2) NOT NULL DEFAULT 0.00 COMMENT 'Break time deducted',
  `worked_hours` DECIMAL(4,2) NOT NULL DEFAULT 0.00 COMMENT 'Total hours worked',
  `is_weekend` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_holiday` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_adjusted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Has been manually adjusted',
  `policy_applied` JSON NULL COMMENT 'Policy rules applied to this record',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. Create holidays table for holiday management
CREATE TABLE IF NOT EXISTS `holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `date` DATE NOT NULL,
  `is_recurring` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Recurring annually',
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_holiday_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Add foreign key for shift_template_id in attendance table (if not exists)
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE
                         WHERE TABLE_SCHEMA = 'attendance_db2'
                         AND TABLE_NAME = 'attendance'
                         AND CONSTRAINT_NAME = 'fk_attendance_shift_template');

SET @sql = IF(@constraint_exists = 0,
              'ALTER TABLE `attendance` ADD CONSTRAINT `fk_attendance_shift_template` FOREIGN KEY (`shift_template_id`) REFERENCES `shift_templates`(`id`) ON DELETE SET NULL',
              'SELECT "Foreign key constraint already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS `idx_attendance_date_employee` ON `attendance`(`employee_id`, `timestamp`);
CREATE INDEX IF NOT EXISTS `idx_attendance_status` ON `attendance`(`attendance_status`);
CREATE INDEX IF NOT EXISTS `idx_attendance_overtime` ON `attendance`(`overtime_hours`);
CREATE INDEX IF NOT EXISTS `idx_holidays_date` ON `holidays`(`date`);
CREATE INDEX IF NOT EXISTS `idx_adjustments_attendance` ON `attendance_adjustments`(`attendance_id`);

-- 8. Insert default holiday data (common holidays)
INSERT IGNORE INTO `holidays` (`name`, `date`, `is_recurring`, `description`) VALUES
('New Year\'s Day', '2024-01-01', TRUE, 'New Year\'s Day'),
('Independence Day', '2024-07-04', TRUE, 'Independence Day'),
('Christmas Day', '2024-12-25', TRUE, 'Christmas Day'),
('Thanksgiving', '2024-11-28', FALSE, 'Thanksgiving Day 2024'),
('Labor Day', '2024-09-02', FALSE, 'Labor Day 2024');

-- 9. Add new permissions for attendance management
INSERT IGNORE INTO `permissions` (`name`, `description`, `category`) VALUES
('attendance.adjust', 'Can manually adjust attendance records', 'attendance'),
('attendance.view_overtime', 'Can view overtime calculations', 'attendance'),
('attendance.manage_policies', 'Can manage attendance policies', 'attendance'),
('holidays.manage', 'Can manage company holidays', 'system');

-- 10. Grant permissions to appropriate roles
-- Super Admin gets all permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'super_admin'
AND p.name IN ('attendance.adjust', 'attendance.view_overtime', 'attendance.manage_policies', 'holidays.manage');

-- Admin gets most permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'admin'
AND p.name IN ('attendance.adjust', 'attendance.view_overtime', 'attendance.manage_policies');

-- Shift Manager gets adjustment permissions
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'shift_manager'
AND p.name IN ('attendance.adjust', 'attendance.view_overtime');

-- HR Manager gets policy management
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.name = 'hr_manager'
AND p.name IN ('attendance.view_overtime', 'attendance.manage_policies');

-- 11. Update existing shift templates with default values
UPDATE `shift_templates` SET
  `early_checkin_allowed_hours` = 1.00,
  `late_checkin_grace_hours` = 0.50,
  `late_checkin_policy` = 'mark_late',
  `early_checkout_penalty_hours` = 0.50,
  `late_checkout_overtime_hours` = 2.00,
  `overtime_rate_multiplier` = 1.50
WHERE `early_checkin_allowed_hours` IS NULL;

-- Migration completed successfully
SELECT 'Advanced Attendance Management System Migration Completed Successfully!' as Status;
