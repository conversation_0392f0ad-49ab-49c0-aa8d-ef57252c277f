<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';
require_once __DIR__ . '/includes/attendance_calculator.php';

$host = "localhost";
$user = "root";
$pass = "";
$db = "attendance_db2";

$mysqli = new mysqli($host, $user, $pass, $db);
if ($mysqli->connect_errno) {
    http_response_code(500);
    die("Database connection failed.");
}

$role = $_SESSION['role'];
$employee_id_session = $_SESSION['employee_id'] ?? 0;

$employee_id = isset($_POST['employee_id']) ? intval($_POST['employee_id']) : 0;
$type = $_POST['type'] ?? null;
$lat = isset($_POST['lat']) ? floatval($_POST['lat']) : null;
$lng = isset($_POST['lng']) ? floatval($_POST['lng']) : null;
$selfieDataUrl = $_POST['selfie'] ?? null;

if ($role !== 'admin') { // employees can only clock for themselves
    $employee_id = $employee_id_session;
}

if (!$employee_id || !$type || !in_array($type, ['in', 'out']) || !$selfieDataUrl) {
    die("Missing or invalid data. <a href='dashboard.php'>Back to Dashboard</a>");
}

// Validate employee exists
$stmt = $mysqli->prepare("SELECT id FROM employees WHERE id = ?");
$stmt->bind_param('i', $employee_id);
$stmt->execute();
$stmt->store_result();
if ($stmt->num_rows === 0) {
    die("Invalid employee. <a href='dashboard.php'>Back to Dashboard</a>");
}
$stmt->close();

// Get shift assignment for today
$today = date('Y-m-d');
$stmt = $mysqli->prepare("
    SELECT es.*, st.name as shift_name, st.start_time, st.end_time
    FROM employee_shifts es
    JOIN shift_templates st ON es.shift_template_id = st.id
    WHERE es.employee_id = ? AND ? BETWEEN es.shift_start_date AND es.shift_end_date
    LIMIT 1
");
$stmt->bind_param('is', $employee_id, $today);
$stmt->execute();
$result = $stmt->get_result();
$shift_assignment = $result->fetch_assoc();
$stmt->close();

$warningMessage = "";
$allowCheckIn = true;

if (!$shift_assignment) {
    $warningMessage = "⚠️ WARNING: You do not have any assigned shifts today. Please contact your shift manager to get a shift assigned. You can still clock in, but this will be flagged for review.";
    $allowCheckIn = true; // Allow check-in but with warning
}

// Save selfie image
if (preg_match('/^data:image\/(\w+);base64,/', $selfieDataUrl, $typeMatches)) {
    $imageType = strtolower($typeMatches[1]);
    if (!in_array($imageType, ['jpeg', 'jpg', 'png', 'gif'])) {
        die("Unsupported image type.");
    }
    $imageData = substr($selfieDataUrl, strpos($selfieDataUrl, ',') + 1);
    $imageData = base64_decode($imageData);

    if ($imageData === false) {
        die("Invalid image data.");
    }

    $uploadDir = __DIR__ . '/selfies/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    $filename = uniqid("selfie_") . '.' . $imageType;
    $filePath = $uploadDir . $filename;

    if (file_put_contents($filePath, $imageData) === false) {
        die("Failed to save image.");
    }

    $selfiePathForDb = 'selfies/' . $filename;
} else {
    die("Invalid selfie format.");
}

// Handle attendance record with advanced calculations
if ($type === 'in') {
    // Check-in logic
    $attendance_metrics = null;

    if ($shift_assignment) {
        // Calculate attendance metrics using the new engine
        $attendance_metrics = calculateAttendanceMetrics(
            $mysqli,
            $shift_assignment,
            date('Y-m-d H:i:s'), // Current timestamp for check-in
            null, // No check-out yet
            $shift_assignment ? getShiftTemplateWithPolicies($mysqli, $shift_assignment['shift_template_id'])['breaks'] : []
        );
    }

    // Insert check-in record with calculated metrics
    $stmt = $mysqli->prepare("
        INSERT INTO attendance (
            employee_id, type, timestamp, lat, lng, selfie_path,
            shift_template_id, scheduled_start_time, scheduled_end_time,
            attendance_status, overtime_hours, late_hours, break_hours, worked_hours,
            is_weekend, is_holiday, policy_applied
        ) VALUES (?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $shift_template_id = $shift_assignment ? $shift_assignment['shift_template_id'] : null;
    $scheduled_start = $shift_assignment ? $shift_assignment['start_time'] : null;
    $scheduled_end = $shift_assignment ? $shift_assignment['end_time'] : null;

    if ($attendance_metrics) {
        $stmt->bind_param('issdsisssddddiss',
            $employee_id, $type, $lat, $lng, $selfiePathForDb,
            $shift_template_id, $scheduled_start, $scheduled_end,
            $attendance_metrics['attendance_status'],
            $attendance_metrics['overtime_hours'],
            $attendance_metrics['late_hours'],
            $attendance_metrics['break_hours'],
            $attendance_metrics['worked_hours'],
            $attendance_metrics['is_weekend'],
            $attendance_metrics['is_holiday'],
            $attendance_metrics['policy_applied']
        );
    } else {
        // No shift assignment - basic record
        $default_status = 'on_time';
        $default_zero = 0.00;
        $default_false = false;
        $default_policy = json_encode(['policy_type' => 'none']);

        $stmt->bind_param('issdsisssddddiss',
            $employee_id, $type, $lat, $lng, $selfiePathForDb,
            $shift_template_id, $scheduled_start, $scheduled_end,
            $default_status, $default_zero, $default_zero, $default_zero, $default_zero,
            $default_false, $default_false, $default_policy
        );
    }

} else {
    // Check-out logic
    // Find the corresponding check-in record for today
    $checkin_stmt = $mysqli->prepare("
        SELECT * FROM attendance
        WHERE employee_id = ? AND type = 'in' AND DATE(timestamp) = CURDATE()
        ORDER BY timestamp DESC LIMIT 1
    ");
    $checkin_stmt->bind_param('i', $employee_id);
    $checkin_stmt->execute();
    $checkin_result = $checkin_stmt->get_result();
    $checkin_record = $checkin_result->fetch_assoc();
    $checkin_stmt->close();

    if (!$checkin_record) {
        die("No check-in record found for today. Please check in first. <a href='dashboard.php'>Back to Dashboard</a>");
    }

    // Calculate complete attendance metrics with check-out
    $attendance_metrics = null;
    if ($shift_assignment) {
        $attendance_metrics = calculateAttendanceMetrics(
            $mysqli,
            $shift_assignment,
            $checkin_record['timestamp'],
            date('Y-m-d H:i:s'), // Current timestamp for check-out
            $shift_assignment ? getShiftTemplateWithPolicies($mysqli, $shift_assignment['shift_template_id'])['breaks'] : []
        );
    }

    // Insert check-out record
    $stmt = $mysqli->prepare("
        INSERT INTO attendance (
            employee_id, type, timestamp, lat, lng, selfie_path,
            shift_template_id, scheduled_start_time, scheduled_end_time,
            attendance_status, overtime_hours, late_hours, break_hours, worked_hours,
            is_weekend, is_holiday, policy_applied
        ) VALUES (?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $shift_template_id = $shift_assignment ? $shift_assignment['shift_template_id'] : null;
    $scheduled_start = $shift_assignment ? $shift_assignment['start_time'] : null;
    $scheduled_end = $shift_assignment ? $shift_assignment['end_time'] : null;

    if ($attendance_metrics) {
        $stmt->bind_param('issdsisssddddiss',
            $employee_id, $type, $lat, $lng, $selfiePathForDb,
            $shift_template_id, $scheduled_start, $scheduled_end,
            $attendance_metrics['attendance_status'],
            $attendance_metrics['overtime_hours'],
            $attendance_metrics['late_hours'],
            $attendance_metrics['break_hours'],
            $attendance_metrics['worked_hours'],
            $attendance_metrics['is_weekend'],
            $attendance_metrics['is_holiday'],
            $attendance_metrics['policy_applied']
        );

        // Update the check-in record with final calculated metrics
        $update_stmt = $mysqli->prepare("
            UPDATE attendance SET
                worked_hours = ?, overtime_hours = ?, break_hours = ?,
                attendance_status = ?, policy_applied = ?
            WHERE id = ?
        ");
        $update_stmt->bind_param('dddssi',
            $attendance_metrics['worked_hours'],
            $attendance_metrics['overtime_hours'],
            $attendance_metrics['break_hours'],
            $attendance_metrics['attendance_status'],
            $attendance_metrics['policy_applied'],
            $checkin_record['id']
        );
        $update_stmt->execute();
        $update_stmt->close();
    } else {
        // No shift assignment - basic record
        $default_status = 'on_time';
        $default_zero = 0.00;
        $default_false = false;
        $default_policy = json_encode(['policy_type' => 'none']);

        $stmt->bind_param('issdsisssddddiss',
            $employee_id, $type, $lat, $lng, $selfiePathForDb,
            $shift_template_id, $scheduled_start, $scheduled_end,
            $default_status, $default_zero, $default_zero, $default_zero, $default_zero,
            $default_false, $default_false, $default_policy
        );
    }
}

if ($stmt->execute()) {
    $stmt->close();

    // Prepare success message with metrics if available
    $successMessage = "Clocked $type successfully.";

    if (isset($attendance_metrics) && $attendance_metrics) {
        if ($attendance_metrics['overtime_hours'] > 0) {
            $successMessage .= " Overtime: " . number_format($attendance_metrics['overtime_hours'], 2) . " hours.";
        }
        if ($attendance_metrics['late_hours'] > 0) {
            $successMessage .= " Late by: " . number_format($attendance_metrics['late_hours'], 2) . " hours.";
        }
        if ($type === 'out' && $attendance_metrics['worked_hours'] > 0) {
            $successMessage .= " Total worked: " . number_format($attendance_metrics['worked_hours'], 2) . " hours.";
        }
    }

    if ($warningMessage) {
        $successMessage .= " " . $warningMessage;
    }

    header("Location: dashboard.php?msg=" . urlencode($successMessage));
    exit;
} else {
    die("Failed to save attendance record.");
}
