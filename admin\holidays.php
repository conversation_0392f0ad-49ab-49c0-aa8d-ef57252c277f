<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

// Check if user has permission to manage holidays
if (!hasPermission($mysqli, $_SESSION['user_id'], 'holidays.manage')) {
    header("Location: ../dashboard.php?error=" . urlencode("You don't have permission to manage holidays."));
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

$msg = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add_holiday') {
            $name = trim($_POST['name'] ?? '');
            $date = $_POST['date'] ?? '';
            $is_recurring = isset($_POST['is_recurring']) ? 1 : 0;
            $description = trim($_POST['description'] ?? '');
            
            if ($name && $date) {
                $stmt = $mysqli->prepare("INSERT INTO holidays (name, date, is_recurring, description) VALUES (?, ?, ?, ?)");
                $stmt->bind_param('ssis', $name, $date, $is_recurring, $description);
                if ($stmt->execute()) {
                    $msg = "Holiday \"$name\" added successfully.";
                } else {
                    $error = "Failed to add holiday. It may already exist for this date.";
                }
                $stmt->close();
            } else {
                $error = "Please fill in all required fields.";
            }
        } elseif ($action === 'delete_holiday') {
            $holiday_id = intval($_POST['holiday_id'] ?? 0);
            if ($holiday_id) {
                $stmt = $mysqli->prepare("DELETE FROM holidays WHERE id = ?");
                $stmt->bind_param('i', $holiday_id);
                if ($stmt->execute()) {
                    $msg = "Holiday deleted successfully.";
                } else {
                    $error = "Failed to delete holiday.";
                }
                $stmt->close();
            }
        }
    }
}

// Fetch all holidays
$holidays_result = $mysqli->query("SELECT * FROM holidays ORDER BY date DESC");
$holidays = $holidays_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Holiday Management - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Holiday Management</h1>
          <p class="page-subtitle">Manage company holidays for attendance policy calculations</p>
        </div>
        <div class="d-flex gap-3">
          <a href="../dashboard.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Add New Holiday -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-plus-circle me-2"></i>
        Add New Holiday
      </h2>
      <form method="POST" class="row g-3">
        <input type="hidden" name="action" value="add_holiday" />

        <div class="col-md-6">
          <label for="name" class="form-label">Holiday Name</label>
          <input type="text" class="form-control" id="name" name="name" required
                 placeholder="e.g., Christmas Day, Independence Day" />
        </div>

        <div class="col-md-6">
          <label for="date" class="form-label">Date</label>
          <input type="date" class="form-control" id="date" name="date" required />
        </div>

        <div class="col-12">
          <label for="description" class="form-label">Description (Optional)</label>
          <textarea class="form-control" id="description" name="description" rows="2"
                    placeholder="Brief description of this holiday"></textarea>
        </div>

        <div class="col-12">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring">
            <label class="form-check-label" for="is_recurring">
              Recurring Holiday (occurs annually on the same date)
            </label>
          </div>
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i>
            Add Holiday
          </button>
        </div>
      </form>
    </div>

    <!-- Existing Holidays -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-calendar-event me-2"></i>
        Company Holidays
      </h2>

      <?php if (empty($holidays)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Holidays Configured</h4>
          <p class="text-muted">Add your first company holiday to get started.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Holiday Name</th>
                <th>Date</th>
                <th>Type</th>
                <th>Description</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($holidays as $holiday): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($holiday['name']) ?></strong>
                </td>
                <td>
                  <?= date('F j, Y', strtotime($holiday['date'])) ?>
                  <small class="text-muted d-block"><?= date('l', strtotime($holiday['date'])) ?></small>
                </td>
                <td>
                  <?php if ($holiday['is_recurring']): ?>
                    <span class="badge bg-info">Recurring</span>
                  <?php else: ?>
                    <span class="badge bg-secondary">One-time</span>
                  <?php endif; ?>
                </td>
                <td><?= htmlspecialchars($holiday['description'] ?: 'No description') ?></td>
                <td>
                  <form method="POST" style="display: inline;"
                        onsubmit="return confirm('Are you sure you want to delete this holiday?')">
                    <input type="hidden" name="action" value="delete_holiday" />
                    <input type="hidden" name="holiday_id" value="<?= $holiday['id'] ?>" />
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                      <i class="bi bi-trash"></i>
                    </button>
                  </form>
                </td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
