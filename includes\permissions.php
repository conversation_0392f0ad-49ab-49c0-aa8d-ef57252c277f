<?php
/**
 * Permissions and Role Management System
 *
 * This file contains all the functions needed to check permissions,
 * manage roles, and handle user authorization throughout the application.
 */

/**
 * Get all permissions for a user
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @return array Array of permission names
 */
function getUserPermissions($mysqli, $user_id) {
    $permissions = [];

    $stmt = $mysqli->prepare("
        SELECT DISTINCT p.name
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ?
    ");

    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $stmt->bind_result($permission_name);

    while ($stmt->fetch()) {
        $permissions[] = $permission_name;
    }

    $stmt->close();
    return $permissions;
}

/**
 * Check if a user has a specific permission
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param string $permission Permission name (e.g., 'employees.create')
 * @return bool True if user has permission, false otherwise
 */
function hasPermission($mysqli, $user_id, $permission) {
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as count
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? AND p.name = ?
    ");

    $stmt->bind_param('is', $user_id, $permission);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();

    return $count > 0;
}

/**
 * Check if a user has any of the specified permissions
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param array $permissions Array of permission names
 * @return bool True if user has at least one permission, false otherwise
 */
function hasAnyPermission($mysqli, $user_id, $permissions) {
    if (empty($permissions)) {
        return false;
    }

    $placeholders = str_repeat('?,', count($permissions) - 1) . '?';
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as count
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? AND p.name IN ($placeholders)
    ");

    $types = 'i' . str_repeat('s', count($permissions));
    $params = array_merge([$user_id], $permissions);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();

    return $count > 0;
}

/**
 * Check if a user has all of the specified permissions
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param array $permissions Array of permission names
 * @return bool True if user has all permissions, false otherwise
 */
function hasAllPermissions($mysqli, $user_id, $permissions) {
    if (empty($permissions)) {
        return true;
    }

    $placeholders = str_repeat('?,', count($permissions) - 1) . '?';
    $stmt = $mysqli->prepare("
        SELECT COUNT(DISTINCT p.name) as count
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? AND p.name IN ($placeholders)
    ");

    $types = 'i' . str_repeat('s', count($permissions));
    $params = array_merge([$user_id], $permissions);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();

    return $count == count($permissions);
}

/**
 * Get all roles for a user
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @return array Array of role information
 */
function getUserRoles($mysqli, $user_id) {
    $roles = [];

    $stmt = $mysqli->prepare("
        SELECT r.id, r.name, r.display_name, r.description
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
        ORDER BY r.display_name
    ");

    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $stmt->bind_result($id, $name, $display_name, $description);

    while ($stmt->fetch()) {
        $roles[] = [
            'id' => $id,
            'name' => $name,
            'display_name' => $display_name,
            'description' => $description
        ];
    }

    $stmt->close();
    return $roles;
}

/**
 * Check if user has a specific role
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param string $role_name Role name
 * @return bool True if user has role, false otherwise
 */
function hasRole($mysqli, $user_id, $role_name) {
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as count
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ? AND r.name = ?
    ");

    $stmt->bind_param('is', $user_id, $role_name);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();

    return $count > 0;
}

/**
 * Require permission or redirect to access denied page
 * @param mysqli $mysqli Database connection
 * @param string $permission Permission name
 * @param string $redirect_url URL to redirect to if access denied (default: dashboard.php)
 */
function requirePermission($mysqli, $permission, $redirect_url = 'dashboard.php') {
    if (!isset($_SESSION['user_id'])) {
        header("Location: login.php");
        exit;
    }

    if (!hasPermission($mysqli, $_SESSION['user_id'], $permission)) {
        $_SESSION['error_message'] = "Access denied. You don't have permission to access this resource.";
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Require any of the specified permissions
 * @param mysqli $mysqli Database connection
 * @param array $permissions Array of permission names
 * @param string $redirect_url URL to redirect to if access denied
 */
function requireAnyPermission($mysqli, $permissions, $redirect_url = 'dashboard.php') {
    if (!isset($_SESSION['user_id'])) {
        header("Location: login.php");
        exit;
    }

    if (!hasAnyPermission($mysqli, $_SESSION['user_id'], $permissions)) {
        $_SESSION['error_message'] = "Access denied. You don't have permission to access this resource.";
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Refresh user permissions and roles in session from database
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID (optional, uses session user_id if not provided)
 */
function refreshUserSession($mysqli, $user_id = null) {
    if ($user_id === null) {
        $user_id = $_SESSION['user_id'] ?? null;
    }

    if (!$user_id) {
        return false;
    }

    // Get fresh user roles and permissions from database
    $user_roles = getUserRoles($mysqli, $user_id);
    $user_permissions = getUserPermissions($mysqli, $user_id);

    // Update session data
    $_SESSION['permissions'] = $user_permissions;
    $_SESSION['roles'] = array_column($user_roles, 'name');

    return true;
}

/**
 * Get all available permissions grouped by category
 * @param mysqli $mysqli Database connection
 * @return array Permissions grouped by category
 */
function getAllPermissions($mysqli) {
    $permissions = [];

    $result = $mysqli->query("
        SELECT id, name, display_name, description, category, resource, action
        FROM permissions
        ORDER BY category, display_name
    ");

    while ($row = $result->fetch_assoc()) {
        $permissions[$row['category']][] = $row;
    }

    return $permissions;
}

/**
 * Get all available roles
 * @param mysqli $mysqli Database connection
 * @return array All roles
 */
function getAllRoles($mysqli) {
    $roles = [];

    $result = $mysqli->query("
        SELECT id, name, display_name, description, is_system_role
        FROM roles
        ORDER BY display_name
    ");

    while ($row = $result->fetch_assoc()) {
        $roles[] = $row;
    }

    return $roles;
}

/**
 * Assign a role to a user
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param int $role_id Role ID
 * @param int $assigned_by User ID of who assigned the role
 * @return bool True on success, false on failure
 */
function assignRoleToUser($mysqli, $user_id, $role_id, $assigned_by = null) {
    $stmt = $mysqli->prepare("
        INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by)
        VALUES (?, ?, ?)
    ");

    $stmt->bind_param('iii', $user_id, $role_id, $assigned_by);
    $result = $stmt->execute();
    $stmt->close();

    return $result;
}

/**
 * Remove a role from a user
 * @param mysqli $mysqli Database connection
 * @param int $user_id User ID
 * @param int $role_id Role ID
 * @return bool True on success, false on failure
 */
function removeRoleFromUser($mysqli, $user_id, $role_id) {
    $stmt = $mysqli->prepare("
        DELETE FROM user_roles
        WHERE user_id = ? AND role_id = ?
    ");

    $stmt->bind_param('ii', $user_id, $role_id);
    $result = $stmt->execute();
    $stmt->close();

    return $result;
}

/**
 * Generate a secure random password
 * @param int $length Password length (default: 12)
 * @return string Generated password
 */
function generateSecurePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';

    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }

    return $password;
}

/**
 * Create a username from employee name
 * @param string $name Employee full name
 * @return string Generated username
 */
function generateUsername($name) {
    // Convert to lowercase and replace spaces with dots
    $username = strtolower(str_replace(' ', '.', trim($name)));

    // Remove special characters except dots
    $username = preg_replace('/[^a-z0-9.]/', '', $username);

    // Remove multiple consecutive dots
    $username = preg_replace('/\.+/', '.', $username);

    // Remove leading/trailing dots
    $username = trim($username, '.');

    return $username;
}
