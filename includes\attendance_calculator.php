<?php
/**
 * Advanced Attendance Calculation Engine
 * Version 2.0.0
 * 
 * Handles overtime, lateness, break time calculations, and policy enforcement
 */

/**
 * Calculate comprehensive attendance status and metrics
 * 
 * @param mysqli $mysqli Database connection
 * @param array $shift_assignment Shift assignment data
 * @param string $checkin_time Check-in timestamp
 * @param string $checkout_time Check-out timestamp (optional)
 * @param array $break_periods Array of break periods for the shift
 * @return array Calculated attendance metrics
 */
function calculateAttendanceMetrics($mysqli, $shift_assignment, $checkin_time, $checkout_time = null, $break_periods = []) {
    // Get shift template with policies
    $shift_template = getShiftTemplateWithPolicies($mysqli, $shift_assignment['shift_template_id']);
    
    // Determine if this is weekend or holiday
    $date = date('Y-m-d', strtotime($checkin_time));
    $is_weekend = isWeekend($date);
    $is_holiday = isHoliday($mysqli, $date);
    
    // Get applicable policy (weekend/holiday override or regular)
    $policy = getApplicablePolicy($mysqli, $shift_template, $is_weekend, $is_holiday);
    
    // Convert times to timestamps for calculation
    $shift_start = strtotime(date('Y-m-d', strtotime($checkin_time)) . ' ' . $shift_template['start_time']);
    $shift_end = strtotime(date('Y-m-d', strtotime($checkin_time)) . ' ' . $shift_template['end_time']);
    $checkin_timestamp = strtotime($checkin_time);
    $checkout_timestamp = $checkout_time ? strtotime($checkout_time) : null;
    
    // Calculate check-in metrics
    $checkin_metrics = calculateCheckinMetrics($shift_start, $checkin_timestamp, $policy);
    
    // Calculate check-out metrics (if available)
    $checkout_metrics = $checkout_timestamp ? 
        calculateCheckoutMetrics($shift_end, $checkout_timestamp, $policy) : 
        ['status' => 'pending', 'overtime_hours' => 0, 'early_departure_hours' => 0];
    
    // Calculate break time
    $break_hours = calculateBreakHours($break_periods, $checkin_timestamp, $checkout_timestamp);
    
    // Calculate total worked hours
    $worked_hours = calculateWorkedHours($checkin_timestamp, $checkout_timestamp, $break_hours);
    
    // Determine overall attendance status
    $overall_status = determineOverallStatus($checkin_metrics['status'], $checkout_metrics['status']);
    
    // Calculate total overtime
    $total_overtime = $checkin_metrics['overtime_hours'] + $checkout_metrics['overtime_hours'];
    
    return [
        'attendance_status' => $overall_status,
        'overtime_hours' => round($total_overtime, 2),
        'late_hours' => round($checkin_metrics['late_hours'], 2),
        'early_departure_hours' => round($checkout_metrics['early_departure_hours'], 2),
        'break_hours' => round($break_hours, 2),
        'worked_hours' => round($worked_hours, 2),
        'is_weekend' => $is_weekend,
        'is_holiday' => $is_holiday,
        'policy_applied' => json_encode([
            'policy_type' => $is_holiday ? 'holiday' : ($is_weekend ? 'weekend' : 'regular'),
            'early_checkin_allowed' => $policy['early_checkin_allowed_hours'],
            'late_checkin_grace' => $policy['late_checkin_grace_hours'],
            'overtime_multiplier' => $policy['overtime_rate_multiplier']
        ]),
        'shift_template_id' => $shift_assignment['shift_template_id'],
        'scheduled_start_time' => $shift_template['start_time'],
        'scheduled_end_time' => $shift_template['end_time']
    ];
}

/**
 * Calculate check-in related metrics
 */
function calculateCheckinMetrics($shift_start, $checkin_timestamp, $policy) {
    $early_limit = $shift_start - ($policy['early_checkin_allowed_hours'] * 3600);
    $late_limit = $shift_start + ($policy['late_checkin_grace_hours'] * 3600);
    
    $overtime_hours = 0;
    $late_hours = 0;
    $status = 'on_time';
    
    if ($checkin_timestamp < $shift_start) {
        // Early check-in
        if ($checkin_timestamp >= $early_limit) {
            // Within allowed early check-in window - calculate overtime
            $overtime_hours = ($shift_start - $checkin_timestamp) / 3600;
            $status = 'early';
        } else {
            // Too early - not allowed
            $status = 'very_early';
        }
    } elseif ($checkin_timestamp > $shift_start) {
        // Late check-in
        if ($checkin_timestamp <= $late_limit) {
            // Within grace period
            $late_hours = ($checkin_timestamp - $shift_start) / 3600;
            $status = 'late';
        } else {
            // Beyond grace period
            $late_hours = ($checkin_timestamp - $shift_start) / 3600;
            $status = $policy['late_checkin_policy'] === 'mark_absent' ? 'absent' : 'very_late';
        }
    }
    
    return [
        'status' => $status,
        'overtime_hours' => $overtime_hours,
        'late_hours' => $late_hours
    ];
}

/**
 * Calculate check-out related metrics
 */
function calculateCheckoutMetrics($shift_end, $checkout_timestamp, $policy) {
    $early_penalty_limit = $shift_end - ($policy['early_checkout_penalty_hours'] * 3600);
    $late_overtime_limit = $shift_end + ($policy['late_checkout_overtime_hours'] * 3600);
    
    $overtime_hours = 0;
    $early_departure_hours = 0;
    $status = 'on_time';
    
    if ($checkout_timestamp < $shift_end) {
        // Early check-out
        if ($checkout_timestamp < $early_penalty_limit) {
            $early_departure_hours = ($shift_end - $checkout_timestamp) / 3600;
            $status = 'early_departure';
        }
    } elseif ($checkout_timestamp > $shift_end) {
        // Late check-out
        if ($checkout_timestamp <= $late_overtime_limit) {
            // Within overtime window
            $overtime_hours = ($checkout_timestamp - $shift_end) / 3600;
            $status = 'overtime';
        } else {
            // Beyond overtime window
            $overtime_hours = $policy['late_checkout_overtime_hours'];
            $status = 'excessive_overtime';
        }
    }
    
    return [
        'status' => $status,
        'overtime_hours' => $overtime_hours,
        'early_departure_hours' => $early_departure_hours
    ];
}

/**
 * Calculate break hours to deduct from worked time
 */
function calculateBreakHours($break_periods, $checkin_timestamp, $checkout_timestamp) {
    if (empty($break_periods) || !$checkout_timestamp) {
        return 0;
    }
    
    $total_break_hours = 0;
    $work_start = $checkin_timestamp;
    $work_end = $checkout_timestamp;
    
    foreach ($break_periods as $break) {
        $break_start = strtotime($break['break_start']);
        $break_end = strtotime($break['break_end']);
        
        // Only count break time if employee was present during break period
        if ($break_start >= $work_start && $break_end <= $work_end) {
            $total_break_hours += ($break_end - $break_start) / 3600;
        }
    }
    
    return $total_break_hours;
}

/**
 * Calculate total worked hours
 */
function calculateWorkedHours($checkin_timestamp, $checkout_timestamp, $break_hours) {
    if (!$checkout_timestamp) {
        return 0; // Can't calculate without checkout
    }
    
    $total_hours = ($checkout_timestamp - $checkin_timestamp) / 3600;
    return max(0, $total_hours - $break_hours);
}

/**
 * Get shift template with attendance policies
 */
function getShiftTemplateWithPolicies($mysqli, $shift_template_id) {
    $stmt = $mysqli->prepare("
        SELECT st.*, 
               sb.break_start, sb.break_end, sb.description as break_description
        FROM shift_templates st
        LEFT JOIN shift_breaks sb ON st.id = sb.shift_template_id
        WHERE st.id = ?
        ORDER BY sb.break_start
    ");
    
    $stmt->bind_param('i', $shift_template_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $shift_template = null;
    $breaks = [];
    
    while ($row = $result->fetch_assoc()) {
        if (!$shift_template) {
            $shift_template = $row;
            $shift_template['breaks'] = [];
        }
        
        if ($row['break_start']) {
            $shift_template['breaks'][] = [
                'break_start' => $row['break_start'],
                'break_end' => $row['break_end'],
                'description' => $row['break_description']
            ];
        }
    }
    
    $stmt->close();
    return $shift_template;
}

/**
 * Get applicable attendance policy (weekend/holiday override or regular)
 */
function getApplicablePolicy($mysqli, $shift_template, $is_weekend, $is_holiday) {
    // Check for holiday policy first
    if ($is_holiday && $shift_template['holiday_policy_enabled']) {
        $stmt = $mysqli->prepare("
            SELECT * FROM attendance_policies 
            WHERE shift_template_id = ? AND policy_type = 'holiday'
        ");
        $stmt->bind_param('i', $shift_template['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($policy = $result->fetch_assoc()) {
            $stmt->close();
            return $policy;
        }
        $stmt->close();
    }
    
    // Check for weekend policy
    if ($is_weekend && $shift_template['weekend_policy_enabled']) {
        $stmt = $mysqli->prepare("
            SELECT * FROM attendance_policies 
            WHERE shift_template_id = ? AND policy_type = 'weekend'
        ");
        $stmt->bind_param('i', $shift_template['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($policy = $result->fetch_assoc()) {
            $stmt->close();
            return $policy;
        }
        $stmt->close();
    }
    
    // Return regular shift template policy
    return $shift_template;
}

/**
 * Check if date is weekend
 */
function isWeekend($date) {
    $day_of_week = date('w', strtotime($date));
    return $day_of_week == 0 || $day_of_week == 6; // Sunday = 0, Saturday = 6
}

/**
 * Check if date is a holiday
 */
function isHoliday($mysqli, $date) {
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as count FROM holidays 
        WHERE date = ? OR (is_recurring = 1 AND DATE_FORMAT(date, '%m-%d') = DATE_FORMAT(?, '%m-%d'))
    ");
    $stmt->bind_param('ss', $date, $date);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();
    
    return $count > 0;
}

/**
 * Determine overall attendance status
 */
function determineOverallStatus($checkin_status, $checkout_status) {
    // Priority order: absent > very_late > late > early_departure > early > on_time
    $status_priority = [
        'absent' => 6,
        'very_late' => 5,
        'late' => 4,
        'early_departure' => 3,
        'early' => 2,
        'on_time' => 1,
        'pending' => 0
    ];
    
    $checkin_priority = $status_priority[$checkin_status] ?? 0;
    $checkout_priority = $status_priority[$checkout_status] ?? 0;
    
    if ($checkin_priority >= $checkout_priority) {
        return $checkin_status;
    } else {
        return $checkout_status;
    }
}
?>
