<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require user management permissions
requireAnyPermission($mysqli, ['users.create', 'users.view', 'users.edit', 'users.delete']);

// Redirect to the new user list page
header("Location: " . appUrl('users/list.php'));
exit;
?>
