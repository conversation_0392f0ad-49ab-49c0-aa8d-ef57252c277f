<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require employee view permissions
requireAnyPermission($mysqli, ['employees.view', 'employees.edit', 'employees.delete']);

$msg = "";
$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['delete_employee']) && isset($_POST['employee_id'])) {
    // Check permission for deleting employees
    if (!hasPermission($mysqli, $_SESSION['user_id'], 'employees.delete')) {
      $error = "You don't have permission to delete employees.";
    } else {
      $employee_id = intval($_POST['employee_id']);

      // Start transaction
      $mysqli->begin_transaction();

      try {
        // Delete associated user account first (if exists)
        $user_stmt = $mysqli->prepare("DELETE FROM users WHERE employee_id = ?");
        $user_stmt->bind_param('i', $employee_id);
        $user_stmt->execute();
        $user_stmt->close();

        // Delete employee
        $emp_stmt = $mysqli->prepare("DELETE FROM employees WHERE id = ?");
        $emp_stmt->bind_param('i', $employee_id);

        if ($emp_stmt->execute()) {
          $emp_stmt->close();
          $mysqli->commit();
          $msg = "Employee and associated user account deleted successfully.";
        } else {
          throw new Exception("Failed to delete employee.");
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = $e->getMessage();
      }
    }
  }
}

// Get employees with their user account information
$result = $mysqli->query("
  SELECT e.id, e.name, e.email, e.phone, e.department, e.position, e.hire_date, e.status,
         u.id as user_id, u.username
  FROM employees e
  LEFT JOIN users u ON e.id = u.employee_id
  ORDER BY e.name
");
$employees = [];
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Employee List - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">


    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Search and Filter -->
    <div class="content-card">
      <div class="row align-items-center">
        <div class="col-md-6">
          <div class="form-group">
            <label for="searchEmployee" class="form-label">
              <i class="bi bi-search me-2"></i>
              Search Employees
            </label>
            <input
              type="text"
              class="form-control"
              id="searchEmployee"
              placeholder="Search by name, email, department..."
              onkeyup="filterEmployees()"
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="filterDepartment" class="form-label">
              <i class="bi bi-funnel me-2"></i>
              Filter by Department
            </label>
            <select class="form-select" id="filterDepartment" onchange="filterEmployees()">
              <option value="">All Departments</option>
              <?php
              $departments = array_unique(array_filter(array_column($employees, 'department')));
              sort($departments);
              foreach ($departments as $dept): ?>
                <option value="<?= htmlspecialchars($dept) ?>"><?= htmlspecialchars($dept) ?></option>
              <?php endforeach; ?>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Employee List -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-people me-2"></i>
        Employees
        <span class="badge bg-primary ms-2" id="employeeCount"><?= count($employees) ?></span>
      </h2>

      <?php if (count($employees) === 0): ?>
        <div class="text-center py-5">
          <i class="bi bi-people" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No employees found.</p>
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.create')): ?>
            <a href="<?= appUrl('employees/create.php') ?>" class="btn btn-primary">
              <i class="bi bi-plus-circle me-2"></i>
              Add Your First Employee
            </a>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table" id="employeeTable">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-person me-2"></i>
                  Employee Details
                </th>
                <th>
                  <i class="bi bi-building me-2"></i>
                  Department & Position
                </th>
                <th>
                  <i class="bi bi-key me-2"></i>
                  Account Status
                </th>
                <th width="150">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($employees as $emp): ?>
                <tr class="employee-row"
                    data-name="<?= strtolower(htmlspecialchars($emp['name'])) ?>"
                    data-email="<?= strtolower(htmlspecialchars($emp['email'])) ?>"
                    data-department="<?= strtolower(htmlspecialchars($emp['department'])) ?>"
                    data-position="<?= strtolower(htmlspecialchars($emp['position'])) ?>">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar me-3">
                        <?= strtoupper(substr($emp['name'], 0, 1)) ?>
                      </div>
                      <div>
                        <strong><?= htmlspecialchars($emp['name']) ?></strong>
                        <br>
                        <?php if ($emp['email']): ?>
                          <small class="text-muted">
                            <i class="bi bi-envelope me-1"></i>
                            <?= htmlspecialchars($emp['email']) ?>
                          </small>
                          <br>
                        <?php endif; ?>
                        <?php if ($emp['phone']): ?>
                          <small class="text-muted">
                            <i class="bi bi-telephone me-1"></i>
                            <?= htmlspecialchars($emp['phone']) ?>
                          </small>
                          <br>
                        <?php endif; ?>
                        <small class="text-muted">ID: <?= $emp['id'] ?></small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <?php if ($emp['department'] || $emp['position']): ?>
                      <?php if ($emp['department']): ?>
                        <span class="badge bg-secondary mb-1"><?= htmlspecialchars($emp['department']) ?></span>
                        <br>
                      <?php endif; ?>
                      <?php if ($emp['position']): ?>
                        <small class="text-muted"><?= htmlspecialchars($emp['position']) ?></small>
                        <br>
                      <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($emp['hire_date']): ?>
                      <small class="text-muted">
                        <i class="bi bi-calendar me-1"></i>
                        Hired: <?= date('M j, Y', strtotime($emp['hire_date'])) ?>
                      </small>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if ($emp['user_id']): ?>
                      <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>
                        Active Account
                      </span>
                      <br>
                      <small class="text-muted">
                        Username: <strong><?= htmlspecialchars($emp['username']) ?></strong>
                      </small>
                      <br>
                      <?php
                      $user_roles = getUserRoles($mysqli, $emp['user_id']);
                      if (!empty($user_roles)): ?>
                        <small class="text-muted">
                          Roles:
                          <?php foreach ($user_roles as $i => $role): ?>
                            <?= htmlspecialchars($role['display_name']) ?><?= $i < count($user_roles) - 1 ? ', ' : '' ?>
                          <?php endforeach; ?>
                        </small>
                      <?php endif; ?>
                    <?php else: ?>
                      <span class="badge bg-warning">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        No Account
                      </span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="btn-group-vertical" role="group">
                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.edit')): ?>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="editEmployee(<?= $emp['id'] ?>)">
                          <i class="bi bi-pencil me-1"></i>
                          Edit
                        </button>
                      <?php endif; ?>

                      <?php if (!$emp['user_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'users.create')): ?>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="createAccount(<?= $emp['id'] ?>)">
                          <i class="bi bi-key me-1"></i>
                          Create Account
                        </button>
                      <?php endif; ?>

                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.delete')): ?>
                        <form
                          method="POST"
                          onsubmit="return confirm('Are you sure you want to delete <?= htmlspecialchars($emp['name']) ?>? All related data will be permanently removed.');"
                          style="display:inline;"
                        >
                          <input type="hidden" name="employee_id" value="<?= $emp['id'] ?>" />
                          <button type="submit" name="delete_employee" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash me-1"></i>
                            Delete
                          </button>
                        </form>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .btn-group-vertical .btn {
      margin-bottom: 0.25rem;
    }

    .table-container {
      overflow-x: auto;
    }

    .employee-row.hidden {
      display: none;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    @media (max-width: 768px) {
      .btn-group-vertical {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .table th, .table td {
        padding: 0.5rem;
        font-size: 0.9rem;
      }

      .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
      }

      .d-flex.gap-3 .btn {
        width: 100%;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function filterEmployees() {
      const searchTerm = document.getElementById('searchEmployee').value.toLowerCase();
      const departmentFilter = document.getElementById('filterDepartment').value.toLowerCase();
      const rows = document.querySelectorAll('.employee-row');
      let visibleCount = 0;

      rows.forEach(row => {
        const name = row.dataset.name || '';
        const email = row.dataset.email || '';
        const department = row.dataset.department || '';
        const position = row.dataset.position || '';

        const matchesSearch = !searchTerm ||
          name.includes(searchTerm) ||
          email.includes(searchTerm) ||
          department.includes(searchTerm) ||
          position.includes(searchTerm);

        const matchesDepartment = !departmentFilter || department === departmentFilter;

        if (matchesSearch && matchesDepartment) {
          row.classList.remove('hidden');
          visibleCount++;
        } else {
          row.classList.add('hidden');
        }
      });

      // Update count
      document.getElementById('employeeCount').textContent = visibleCount;
    }

    function editEmployee(employeeId) {
      window.location.href = `edit.php?id=${employeeId}`;
    }

    function createAccount(employeeId) {
      // TODO: Implement create account functionality
      alert('Create account functionality will be implemented in the next update.');
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Add any initialization code here
    });
  </script>
</body>
</html>
